import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { StorageProvider } from '../../common/enums/storage.enum';
import { Diary } from './diary.entity';

@Entity()
export class DiaryCoverRegistry {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Basic file information
  @Column({ name: 'file_path' })
  filePath: string;

  @Column({ name: 'file_name' })
  fileName: string;

  @Column({ name: 'mime_type', nullable: true })
  mimeType: string;

  @Column({ name: 'file_size', nullable: true })
  fileSize: number;

  // S3 Storage Support Columns
  @Column({
    name: 'storage_provider',
    type: 'enum',
    enum: StorageProvider,
    default: StorageProvider.LOCAL
  })
  storageProvider: StorageProvider;

  @Column({ name: 'storage_key', nullable: true, length: 500 })
  storageKey: string;

  @Column({ name: 's3_bucket', nullable: true, length: 100 })
  s3Bucket: string;

  @Column({ name: 's3_region', nullable: true, length: 50 })
  s3Region: string;

  @Column({ name: 's3_etag', nullable: true, length: 100 })
  s3Etag: string;

  @Column({ name: 's3_version_id', nullable: true, length: 100 })
  s3VersionId: string;

  @Column({ name: 's3_storage_class', nullable: true, length: 50 })
  s3StorageClass: string;

  @Column({ name: 's3_server_side_encryption', nullable: true, length: 50 })
  s3ServerSideEncryption: string;

  @Column({ name: 'cdn_url', nullable: true, length: 500 })
  cdnUrl: string;
  @Column({ name: 'diary_id' })
  diaryId: string;

  @ManyToOne(() => Diary, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'diary_id' })
  diary: Diary;

  @Column({ name: 'user_id' })
  userId: string;

  /**
   * Get file size in human readable format
   */
  getFormattedFileSize(): string {
    if (!this.fileSize) return 'Unknown';

    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = this.fileSize;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  /**
   * Check if the file is an image
   */
  isImage(): boolean {
    if (!this.mimeType) return false;
    return this.mimeType.startsWith('image/');
  }

  /**
   * Convert to DTO for API responses
   */
  toDto(): any {
    return {
      id: this.id,
      fileName: this.fileName,
      mimeType: this.mimeType,
      fileSize: this.fileSize,
      formattedFileSize: this.getFormattedFileSize(),
      storageProvider: this.storageProvider,
      isImage: this.isImage(),
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      diaryId: this.diaryId,
      userId: this.userId,
      diary: this.diary ? {
        id: this.diary.id,
        userId: this.diary.userId,
        defaultSkinId: this.diary.defaultSkinId
      } : null
    };
  }
}
