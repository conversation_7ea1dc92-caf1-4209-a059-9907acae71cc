import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateDiaryCoverRegistry1748530000000 implements MigrationInterface {
  name = 'CreateDiaryCoverRegistry1748530000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if table already exists
    const tableExists = await queryRunner.hasTable('diary_cover_registry');
    if (tableExists) {
      console.log('Table diary_cover_registry already exists, skipping creation');
      return;
    }

    // Create diary_cover_registry table
    await queryRunner.query(`
      CREATE TABLE "diary_cover_registry" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "diary_id" uuid NOT NULL,
        "user_id" uuid NOT NULL,
        "file_path" character varying(500) NOT NULL,
        "file_name" character varying(255) NOT NULL,
        "mime_type" character varying(100),
        "file_size" bigint,
        "storage_provider" character varying(50) DEFAULT 'local',
        "storage_key" character varying(500),
        "s3_bucket" character varying(255),
        "s3_region" character varying(50),
        "s3_etag" character varying(255),
        "s3_version_id" character varying(255),
        "s3_storage_class" character varying(50),
        "s3_server_side_encryption" character varying(50),
        "cdn_url" character varying(500),
        "storage_metadata" jsonb,
        "presigned_url_expires_at" TIMESTAMP WITH TIME ZONE,
        "is_migrated" boolean NOT NULL DEFAULT false,
        "migration_status" character varying(20),
        "migration_error" text,
        "migrated_at" TIMESTAMP WITH TIME ZONE,
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(),
        "created_by" character varying(36),
        "updated_by" character varying(36),
        CONSTRAINT "PK_diary_cover_registry_id" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_diary_cover_registry_diary_id" UNIQUE ("diary_id")
      )
    `);

    // Add foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "diary_cover_registry"
      ADD CONSTRAINT "FK_diary_cover_registry_diary"
      FOREIGN KEY ("diary_id") REFERENCES "diary"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "diary_cover_registry"
      ADD CONSTRAINT "FK_diary_cover_registry_user"
      FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE CASCADE
    `);

    // Add indexes for better performance
    await queryRunner.query(`CREATE UNIQUE INDEX "IDX_diary_cover_registry_diary_id" ON "diary_cover_registry" ("diary_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_diary_cover_registry_user_id" ON "diary_cover_registry" ("user_id")`);

    console.log('Created diary_cover_registry table');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_diary_cover_registry_user_id"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_diary_cover_registry_diary_id"`);

    // Drop foreign key constraints
    await queryRunner.query(`ALTER TABLE "diary_cover_registry" DROP CONSTRAINT IF EXISTS "FK_diary_cover_registry_user"`);
    await queryRunner.query(`ALTER TABLE "diary_cover_registry" DROP CONSTRAINT IF EXISTS "FK_diary_cover_registry_diary"`);

    // Drop table
    await queryRunner.query(`DROP TABLE IF EXISTS "diary_cover_registry"`);

    console.log('Dropped diary_cover_registry table');
  }
}
