import { MigrationInterface, QueryRunner, Table, Index } from 'typeorm';

export class CreateDiaryCoverRegistry1748530000000 implements MigrationInterface {
  name = 'CreateDiaryCoverRegistry1748530000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if table already exists
    const tableExists = await queryRunner.hasTable('diary_cover_registry');
    if (tableExists) {
      console.log('Table diary_cover_registry already exists, skipping creation');
      return;
    }

    await queryRunner.createTable(
      new Table({
        name: 'diary_cover_registry',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'diary_id',
            type: 'uuid',
            isNullable: false,
            isUnique: true, // Ensure only one cover photo per diary
          },
          {
            name: 'user_id',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'file_path',
            type: 'varchar',
            length: '500',
            isNullable: false,
          },
          {
            name: 'file_name',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'mime_type',
            type: 'varchar',
            length: '100',
            isNullable: false,
          },
          {
            name: 'file_size',
            type: 'bigint',
            isNullable: false,
          },
          {
            name: 'storage_provider',
            type: 'varchar',
            length: '50',
            isNullable: true,
            default: "'local'",
          },
          {
            name: 'storage_key',
            type: 'varchar',
            length: '500',
            isNullable: true,
          },
          {
            name: 's3_bucket',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 's3_region',
            type: 'varchar',
            length: '50',
            isNullable: true,
          },
          {
            name: 's3_etag',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 's3_version_id',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 's3_storage_class',
            type: 'varchar',
            length: '50',
            isNullable: true,
          },
          {
            name: 's3_server_side_encryption',
            type: 'varchar',
            length: '50',
            isNullable: true,
          },
          {
            name: 'cdn_url',
            type: 'varchar',
            length: '500',
            isNullable: true,
          },
          {
            name: 'storage_metadata',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'presigned_url_expires_at',
            type: 'timestamp with time zone',
            isNullable: true,
          },
          {
            name: 'is_migrated',
            type: 'boolean',
            default: false,
          },
          {
            name: 'migration_status',
            type: 'varchar',
            length: '20',
            isNullable: true,
          },
          {
            name: 'migration_error',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'migrated_at',
            type: 'timestamp with time zone',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'timestamp with time zone',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'timestamp with time zone',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'created_by',
            type: 'varchar',
            length: '36',
            isNullable: true,
          },
          {
            name: 'updated_by',
            type: 'varchar',
            length: '36',
            isNullable: true,
          },
        ],
        foreignKeys: [
          {
            columnNames: ['diary_id'],
            referencedTableName: 'diary',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
          {
            columnNames: ['user_id'],
            referencedTableName: 'user',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
        ],
        indices: [
          {
            name: 'IDX_diary_cover_registry_diary_id',
            columnNames: ['diary_id'],
            isUnique: true,
          },
          {
            name: 'IDX_diary_cover_registry_user_id',
            columnNames: ['user_id'],
          },
        ],
      }),
      true
    );

    console.log('Created diary_cover_registry table');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('diary_cover_registry', true);
    console.log('Dropped diary_cover_registry table');
  }
}
