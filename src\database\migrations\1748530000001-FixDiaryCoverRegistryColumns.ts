import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixDiaryCoverRegistryColumns1748530000001 implements MigrationInterface {
  name = 'FixDiaryCoverRegistryColumns1748530000001';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if table exists
    const tableExists = await queryRunner.hasTable('diary_cover_registry');
    
    if (!tableExists) {
      console.log('Table diary_cover_registry does not exist, creating it...');
      
      // Create the complete table with all required columns
      await queryRunner.query(`
        CREATE TABLE "diary_cover_registry" (
          "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "diary_id" uuid NOT NULL,
          "user_id" uuid NOT NULL,
          "file_path" character varying(500) NOT NULL,
          "file_name" character varying(255) NOT NULL,
          "mime_type" character varying(100),
          "file_size" bigint,
          "storage_provider" character varying(50) DEFAULT 'local',
          "storage_key" character varying(500),
          "s3_bucket" character varying(255),
          "s3_region" character varying(50),
          "s3_etag" character varying(255),
          "s3_version_id" character varying(255),
          "s3_storage_class" character varying(50),
          "s3_server_side_encryption" character varying(50),
          "cdn_url" character varying(500),
          "storage_metadata" jsonb,
          "presigned_url_expires_at" TIMESTAMP WITH TIME ZONE,
          "is_migrated" boolean NOT NULL DEFAULT false,
          "migration_status" character varying(20),
          "migration_error" text,
          "migrated_at" TIMESTAMP WITH TIME ZONE,
          "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
          "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(),
          "created_by" character varying(36),
          "updated_by" character varying(36),
          CONSTRAINT "PK_diary_cover_registry_id" PRIMARY KEY ("id"),
          CONSTRAINT "UQ_diary_cover_registry_diary_id" UNIQUE ("diary_id")
        )
      `);

      // Add foreign key constraints
      await queryRunner.query(`
        ALTER TABLE "diary_cover_registry"
        ADD CONSTRAINT "FK_diary_cover_registry_diary"
        FOREIGN KEY ("diary_id") REFERENCES "diary"("id") ON DELETE CASCADE
      `);

      await queryRunner.query(`
        ALTER TABLE "diary_cover_registry"
        ADD CONSTRAINT "FK_diary_cover_registry_user"
        FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE CASCADE
      `);

      // Add indexes
      await queryRunner.query(`CREATE UNIQUE INDEX "IDX_diary_cover_registry_diary_id" ON "diary_cover_registry" ("diary_id")`);
      await queryRunner.query(`CREATE INDEX "IDX_diary_cover_registry_user_id" ON "diary_cover_registry" ("user_id")`);

      console.log('Created diary_cover_registry table with all required columns');
    } else {
      console.log('Table diary_cover_registry exists, checking for missing columns...');
      
      // Check and add missing columns one by one
      const hasCreatedBy = await queryRunner.hasColumn('diary_cover_registry', 'created_by');
      const hasUpdatedBy = await queryRunner.hasColumn('diary_cover_registry', 'updated_by');
      const hasStorageProvider = await queryRunner.hasColumn('diary_cover_registry', 'storage_provider');
      const hasIsMigrated = await queryRunner.hasColumn('diary_cover_registry', 'is_migrated');
      
      if (!hasCreatedBy) {
        await queryRunner.query(`ALTER TABLE "diary_cover_registry" ADD COLUMN "created_by" character varying(36)`);
        console.log('Added created_by column');
      }
      
      if (!hasUpdatedBy) {
        await queryRunner.query(`ALTER TABLE "diary_cover_registry" ADD COLUMN "updated_by" character varying(36)`);
        console.log('Added updated_by column');
      }
      
      if (!hasStorageProvider) {
        await queryRunner.query(`ALTER TABLE "diary_cover_registry" ADD COLUMN "storage_provider" character varying(50) DEFAULT 'local'`);
        console.log('Added storage_provider column');
      }
      
      if (!hasIsMigrated) {
        await queryRunner.query(`ALTER TABLE "diary_cover_registry" ADD COLUMN "is_migrated" boolean NOT NULL DEFAULT false`);
        console.log('Added is_migrated column');
      }

      // Add other missing columns if needed
      const missingColumns = [
        { name: 'storage_key', type: 'character varying(500)' },
        { name: 's3_bucket', type: 'character varying(255)' },
        { name: 's3_region', type: 'character varying(50)' },
        { name: 's3_etag', type: 'character varying(255)' },
        { name: 's3_version_id', type: 'character varying(255)' },
        { name: 's3_storage_class', type: 'character varying(50)' },
        { name: 's3_server_side_encryption', type: 'character varying(50)' },
        { name: 'cdn_url', type: 'character varying(500)' },
        { name: 'storage_metadata', type: 'jsonb' },
        { name: 'presigned_url_expires_at', type: 'TIMESTAMP WITH TIME ZONE' },
        { name: 'migration_status', type: 'character varying(20)' },
        { name: 'migration_error', type: 'text' },
        { name: 'migrated_at', type: 'TIMESTAMP WITH TIME ZONE' }
      ];

      for (const column of missingColumns) {
        const hasColumn = await queryRunner.hasColumn('diary_cover_registry', column.name);
        if (!hasColumn) {
          await queryRunner.query(`ALTER TABLE "diary_cover_registry" ADD COLUMN "${column.name}" ${column.type}`);
          console.log(`Added ${column.name} column`);
        }
      }

      // Ensure unique constraint exists
      try {
        await queryRunner.query(`ALTER TABLE "diary_cover_registry" ADD CONSTRAINT "UQ_diary_cover_registry_diary_id" UNIQUE ("diary_id")`);
        console.log('Added unique constraint on diary_id');
      } catch (error) {
        console.log('Unique constraint on diary_id already exists or failed to add');
      }
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // This migration is designed to fix missing columns, so rollback would remove the table
    await queryRunner.query(`DROP TABLE IF EXISTS "diary_cover_registry"`);
    console.log('Dropped diary_cover_registry table');
  }
}
