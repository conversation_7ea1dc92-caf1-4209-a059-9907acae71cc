import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsUUID, IsEnum } from 'class-validator';
import { FriendshipStatus } from '../entities/student-friendship.entity';
import { FollowRequestStatus } from '../entities/diary-follow-request.entity';

/**
 * DTO for searching for students
 */
export class SearchStudentsDto {
  @ApiProperty({
    description: 'Search query (name, ID, email, or phone)',
    example: 'ANNA33',
    required: true
  })
  @IsNotEmpty()
  @IsString()
  query: string;

  @ApiProperty({
    description: 'Search type (id, name, email, phone)',
    example: 'id',
    enum: ['id', 'name', 'email', 'phone'],
    required: false
  })
  @IsOptional()
  @IsString()
  type?: string;
}

/**
 * DTO for sending a friend request
 */
export class SendFriendRequestDto {
  @ApiProperty({
    description: 'ID of the student to send the request to',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsNotEmpty()
  @IsUUID()
  requestedId: string;

  @ApiProperty({
    description: 'Optional message to include with the request',
    example: 'Hi, I would like to be friends!',
    required: false
  })
  @IsOptional()
  @IsString()
  requestMessage?: string;
}

/**
 * DTO for sending a diary follow request
 */
export class SendDiaryFollowRequestDto {
  @ApiProperty({
    description: 'ID of the student whose diary you want to follow',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsNotEmpty()
  @IsUUID()
  diaryOwnerId: string;

  @ApiProperty({
    description: 'Optional message to include with the request',
    example: 'I would like to follow your diary!',
    required: false
  })
  @IsOptional()
  @IsString()
  requestMessage?: string;
}

/**
 * DTO for responding to a friend or diary follow request
 */
export class RespondToRequestDto {
  @ApiProperty({
    description: 'Status to set for the request',
    enum: [FriendshipStatus.ACCEPTED, FriendshipStatus.REJECTED],
    example: FriendshipStatus.ACCEPTED
  })
  @IsNotEmpty()
  @IsEnum(FriendshipStatus, {
    message: 'Status must be either accepted or rejected'
  })
  status: FriendshipStatus;
}

/**
 * Response DTO for student search results
 */
export class StudentSearchResultDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  userId: string;

  @ApiProperty()
  name: string;

  @ApiProperty({ required: false })
  profilePicture?: string;

  @ApiProperty({ enum: ['none', 'pending', 'accepted', 'rejected'] })
  friendshipStatus: string;

  @ApiProperty()
  isFriend: boolean;

  @ApiProperty()
  canViewDiary: boolean;
}

/**
 * Response DTO for friendship details
 */
export class FriendshipResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  requesterId: string;

  @ApiProperty()
  requesterName: string;

  @ApiProperty({ required: false })
  requesterProfilePicture?: string;

  @ApiProperty()
  requestedId: string;

  @ApiProperty()
  requestedName: string;

  @ApiProperty({ required: false })
  requestedProfilePicture?: string;

  @ApiProperty({ enum: FriendshipStatus })
  status: FriendshipStatus;

  @ApiProperty({ required: false })
  requestMessage?: string;

  @ApiProperty()
  canViewDiary: boolean;

  @ApiProperty()
  createdAt: Date;

  // Friend-specific fields for easier client-side handling
  @ApiProperty({ description: 'ID of the friend (from current user perspective)' })
  friendId: string;

  @ApiProperty({ description: 'Name of the friend (from current user perspective)' })
  friendName: string;

  @ApiProperty({ required: false, description: 'Profile picture of the friend (from current user perspective)' })
  friendProfilePicture?: string;
}

/**
 * Response DTO for pending friend requests
 */
export class PendingRequestsResponseDto {
  @ApiProperty({ type: [FriendshipResponseDto] })
  incomingRequests: FriendshipResponseDto[];

  @ApiProperty({ type: [FriendshipResponseDto] })
  outgoingRequests: FriendshipResponseDto[];
}

/**
 * Response DTO for diary follow request details
 */
export class DiaryFollowRequestResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  requesterId: string;

  @ApiProperty()
  requesterName: string;

  @ApiProperty({ required: false })
  requesterProfilePicture?: string;

  @ApiProperty()
  diaryOwnerId: string;

  @ApiProperty()
  diaryOwnerName: string;

  @ApiProperty({ required: false })
  diaryOwnerProfilePicture?: string;

  @ApiProperty({ enum: FollowRequestStatus })
  status: FollowRequestStatus;

  @ApiProperty({ required: false })
  requestMessage?: string;

  @ApiProperty()
  createdAt: Date;
}
